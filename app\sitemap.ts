import { MetadataRoute } from "next";
import { fetchBlogs } from "@/lib/appwrite";

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  // Fetch actual blogs from Appwrite
  const blogs = await fetchBlogs() || [];
  const baseUrl = "https://www.flerid.in";

  const routes = [
    { url: baseUrl, lastModified: new Date().toISOString() },
    { url: `${baseUrl}/about`, lastModified: new Date().toISOString() },
    { url: `${baseUrl}/blogs`, lastModified: new Date().toISOString() },
    { url: `${baseUrl}/pricing`, lastModified: new Date().toISOString() },
    { url: `${baseUrl}/services`, lastModified: new Date().toISOString() },
    { url: `${baseUrl}/contact`, lastModified: new Date().toISOString() },
    { url: `${baseUrl}/portfolio`, lastModified: new Date().toISOString() }, // Added portfolio page
  ];

  // Add blog routes with proper error handling
  const blogRoutes = blogs.map((blog: any) => ({
    url: `${baseUrl}/blogs/${blog.slug || blog.$id}`,
    lastModified: blog.$updatedAt || new Date().toISOString(),
  }));

  return [...routes, ...blogRoutes];
}