import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON>eist } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "next-themes";
import { Toaster } from "@/components/ui/sonner";
import { AppSidebar } from "@/components/app-sidebar";
import Footer from "@/components/footer";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Flerid Technologies",
  description:
    "Flerid | Your Gateway to Professional Web Development, App Development, Digital Marketing & Social Media Solutions",
  manifest: "/manifest.json",
  icons: {
    icon: [
      { url: '/favicon.ico', sizes: '32x32', type: 'image/x-icon' },
      { url: '/favicon.ico', sizes: '16x16', type: 'image/x-icon' },
    ],
    shortcut: '/favicon.ico',
    apple: [
      { url: '/favicon.ico', sizes: '180x180', type: 'image/x-icon' },
    ],
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} antialiased min-h-screen`}
        suppressHydrationWarning
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="dark"
          enableSystem
          disableTransitionOnChange
        >
          <div className="relative flex min-h-screen">
            {/* Sidebar */}
            <div className="fixed left-0 top-0 z-40 h-screen">
              <AppSidebar />
            </div>

            {/* Main Content */}
            <div className="flex-1 flex flex-col md:pl-[60px] transition-all duration-300">
              <main className="flex-1 min-h-screen w-full">
                <Toaster
                  position="top-right"
                  closeButton
                />
                {/* <BackgroundLines> */}
                  <div>
                    {children}
                  </div>
                  <Footer />
                {/* </BackgroundLines> */}
              </main>
            </div>
          </div>
        </ThemeProvider>
      </body>
    </html>
  );
}