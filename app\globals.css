@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom Scrollbar Styles */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  @apply bg-muted;
}

::-webkit-scrollbar-thumb {
  @apply bg-gradient-to-b from-blue-600 to-cyan-600 rounded-full;
  border: 2px solid transparent;
  background-clip: padding-box;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gradient-to-b from-blue-700 to-cyan-700;
}

/* For Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: theme('colors.blue.600') theme('colors.gray.200');
}

.dark * {
  scrollbar-color: theme('colors.blue.500') theme('colors.gray.800');
}

/* Service Button Text Visibility Fix */
.service-button-text {
  color: white !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7) !important;
  font-weight: 500 !important;
  text-decoration: none !important;
}

.service-button-text:hover {
  color: white !important;
  text-decoration: none !important;
}

/* Gradient Heading Enhancements */
@keyframes gradient-x {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.animate-gradient-x {
  animation: gradient-x 6s ease infinite;
}

.bg-size-200 {
  background-size: 200% 200%;
}

/* Enhanced Blog Content Styling */
.blog-content {
  font-family: 'Geist', system-ui, -apple-system, sans-serif;
  line-height: 1.7;
  font-feature-settings: "liga" 1, "kern" 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Responsive Typography */
@media (max-width: 640px) {
  .blog-content {
    font-size: 16px;
    line-height: 1.6;
  }

  .blog-content h1 {
    font-size: 2rem !important;
    line-height: 1.2 !important;
  }

  .blog-content h2 {
    font-size: 1.75rem !important;
    line-height: 1.3 !important;
  }

  .blog-content h3 {
    font-size: 1.5rem !important;
    line-height: 1.4 !important;
  }

  .blog-content p:first-of-type::first-letter {
    font-size: 3rem;
    line-height: 2.5rem;
  }
}

.blog-content h1,
.blog-content h2 {
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradient-x 6s ease infinite;
}

.blog-content p:first-of-type::first-letter {
  float: left;
  font-size: 4rem;
  line-height: 3.5rem;
  padding-right: 0.5rem;
  padding-top: 0.25rem;
  font-weight: bold;
  background: linear-gradient(135deg, #2563eb, #06b6d4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.blog-content blockquote::before {
  content: '"';
  font-size: 4rem;
  color: #2563eb;
  position: absolute;
  left: 1rem;
  top: 0.5rem;
  font-family: Georgia, serif;
  opacity: 0.3;
}

.blog-content blockquote {
  position: relative;
  padding-left: 4rem;
}

.blog-content pre code {
  background: none !important;
  color: inherit !important;
  padding: 0 !important;
  border-radius: 0 !important;
}

.blog-content img {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.blog-content img:hover {
  transform: scale(1.02);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Reading progress indicator */
.blog-content::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, #2563eb, #06b6d4);
  transform-origin: left;
  z-index: 1000;
  animation: reading-progress linear;
  animation-timeline: scroll(root);
}

@supports (animation-timeline: scroll()) {
  .blog-content::before {
    animation: reading-progress linear;
    animation-timeline: scroll(root);
  }
}

@keyframes reading-progress {
  from {
    transform: scaleX(0);
  }
  to {
    transform: scaleX(1);
  }
}

/* Focus and Selection Styling */
.blog-content *::selection {
  background: rgba(37, 99, 235, 0.2);
  color: inherit;
}

.blog-content *::-moz-selection {
  background: rgba(37, 99, 235, 0.2);
  color: inherit;
}

/* Smooth scrolling for anchor links */
.blog-content {
  scroll-behavior: smooth;
}

/* Enhanced link hover effects */
.blog-content a {
  position: relative;
  transition: all 0.3s ease;
}

.blog-content a::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -2px;
  left: 0;
  background: linear-gradient(90deg, #2563eb, #06b6d4);
  transition: width 0.3s ease;
}

.blog-content a:hover::after {
  width: 100%;
}

/* Print styles */
@media print {
  .blog-content {
    font-size: 12pt;
    line-height: 1.5;
    color: black !important;
  }

  .blog-content h1,
  .blog-content h2,
  .blog-content h3,
  .blog-content h4 {
    color: black !important;
    background: none !important;
    -webkit-text-fill-color: black !important;
  }

  .blog-content img {
    max-width: 100% !important;
    height: auto !important;
    box-shadow: none !important;
  }
}
