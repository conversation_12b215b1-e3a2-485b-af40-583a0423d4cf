{"name": "flerid", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@radix-ui/react-dialog": "^1.1.5", "@radix-ui/react-dropdown-menu": "^2.1.5", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.5", "@radix-ui/react-select": "^2.1.5", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.7", "@tabler/icons-react": "^3.29.0", "appwrite": "^16.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.0.5", "lucide-react": "^0.474.0", "next": "15.1.6", "next-themes": "^0.4.4", "react": "^19.0.0", "react-day-picker": "^9.8.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-markdown": "^9.0.3", "remark-gfm": "^4.0.0", "sonner": "^1.7.2", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "turndown": "^7.2.0", "zod": "^3.24.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/turndown": "^5.0.5", "eslint": "^9", "eslint-config-next": "15.1.6", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}