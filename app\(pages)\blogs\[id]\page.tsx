"use client";

import { useEffect, useState } from "react";
import React from "react";
import { ArrowLeft, Calendar, Clock, Share2, Loader2, <PERSON><PERSON><PERSON>, Eye } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import Image from "next/image";
import { motion } from "framer-motion";
import { cn, getAppwriteImageUrl } from "@/lib/utils";
import { fetchBlogByIdOrSlug } from "@/lib/appwrite";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import TurndownService from "turndown";

const turndownService = new TurndownService({
  headingStyle: "atx",
  codeBlock: true,
  bulletListMarker: "-",
  blankReplacement: function(content, node) {
    return node.isBlock ? '\n\n' : '';
  }
});

// Configure heading rules
turndownService.addRule("paragraphs", {
  filter: "p",
  replacement: function(content) {
    return '\n\n' + content + '\n\n';
  }
});

turndownService.addRule("headings", {
  filter: ["h1", "h2", "h3", "h4", "h5", "h6"],
  replacement: function (content, node) {
    return '\n\n**' + content + '**\n\n';
  },
});

export default function BlogPost({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const resolvedParams = React.use(params);
  const [blog, setBlog] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);


  useEffect(() => {
    fetchBlogData();
  }, [resolvedParams.id]);

  // Reading progress effect
  useEffect(() => {
    const updateReadingProgress = () => {
      const article = document.querySelector('.blog-content');
      const progressBar = document.getElementById('reading-progress');

      if (article && progressBar) {
        const articleTop = article.offsetTop;
        const articleHeight = article.offsetHeight;
        const windowHeight = window.innerHeight;
        const scrollTop = window.scrollY;

        const progress = Math.min(
          Math.max((scrollTop - articleTop + windowHeight / 2) / articleHeight, 0),
          1
        );

        progressBar.style.width = `${progress * 100}%`;
      }
    };

    window.addEventListener('scroll', updateReadingProgress);
    updateReadingProgress(); // Initial call

    return () => window.removeEventListener('scroll', updateReadingProgress);
  }, [blog]);

  const fetchBlogData = async () => {
    try {
      const data = await fetchBlogByIdOrSlug(resolvedParams.id);
      setBlog(data);
    } catch (error) {
      console.error("Error fetching blog:", error);
      setError("Failed to load blog post");
    } finally {
      setIsLoading(false);
    }
  };

  const convertHtmlToMarkdown = (html: string) => {
    let markdown = turndownService.turndown(html);

    // Ensure proper line breaks between paragraphs
    markdown = markdown.replace(/\n\s*\n/g, '\n\n');
    
    // Remove any triple or more line breaks
    markdown = markdown.replace(/\n{3,}/g, '\n\n');
    
    // Remove escape characters
    markdown = markdown.replace(/\\/g, '');

    return markdown.trim();
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: blog.title,
        url: window.location.href,
      });
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-950">
        <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
      </div>
    );
  }

  if (error || !blog) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-950">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center px-4"
        >
          <h1 className="text-3xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-cyan-600">
            {error || "Blog Post Not Found"}
          </h1>
          <p className="text-muted-foreground mb-6">
            The article you're looking for doesn't exist or has been removed.
          </p>
          <Link href="/blogs">
            <Button variant="outline" size="lg">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Articles
            </Button>
          </Link>
        </motion.div>
      </div>
    );
  }

  const readingTime = Math.ceil(blog.content.split(" ").length / 200); // Assuming 200 words per minute

  return (
    <article className="min-h-screen bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-950">
      {/* Hero Section */}
      <div className="relative">
        {blog.featuredImage && (
          <div className="relative w-full h-[60vh] min-h-[400px]">
            <Image
              src={
                getAppwriteImageUrl(blog.featuredImage) ||
                "/placeholder-blog.jpg"
              }
              alt={blog.title}
              fill
              className="object-cover"
              priority
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = "/placeholder-blog.jpg";
                target.onerror = null;
              }}
            />
            <div className="absolute inset-0 bg-gradient-to-t from-background via-background/50 to-transparent" />
          </div>
        )}

        <div className="container mx-auto px-4">
          <div className="relative py-12 max-w-4xl mx-auto">
            <Link href="/blogs">
              <Button
                variant="ghost"
                className="mb-8 hover:bg-background/80 backdrop-blur-sm"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Articles
              </Button>
            </Link>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <h1 className="text-4xl md:text-5xl font-bold mb-6">
                {blog.title}
              </h1>

              {/* Enhanced Metadata Section */}
              <div className="flex flex-wrap items-center gap-6 mb-8">
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Calendar className="w-4 h-4 text-blue-600" />
                  <time dateTime={blog.$createdAt} className="text-sm font-medium">
                    {new Date(blog.$createdAt).toLocaleDateString("en-US", {
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                    })}
                  </time>
                </div>
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Clock className="w-4 h-4 text-blue-600" />
                  <span className="text-sm font-medium">{readingTime} min read</span>
                </div>
                <div className="flex items-center gap-2 text-muted-foreground">
                  <BookOpen className="w-4 h-4 text-blue-600" />
                  <span className="text-sm font-medium">{blog.content.split(" ").length} words</span>
                </div>
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Eye className="w-4 h-4 text-blue-600" />
                  <span className="text-sm font-medium">Article</span>
                </div>
                {navigator.share && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="ml-auto hover:bg-blue-50 dark:hover:bg-blue-950/30 transition-colors"
                    onClick={handleShare}
                  >
                    <Share2 className="w-4 h-4 mr-2 text-blue-600" />
                    <span className="text-blue-600 font-medium">Share</span>
                  </Button>
                )}
              </div>

              {/* Reading Progress Bar */}
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1 mb-8">
                <div className="bg-gradient-to-r from-blue-600 to-cyan-600 h-1 rounded-full transition-all duration-300" style={{width: '0%'}} id="reading-progress"></div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Content Section */}
      <div className="container mx-auto px-4 py-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="max-w-4xl mx-auto"
        >
          {/* Enhanced Blog Content with Custom Typography */}
          <div className="blog-content">
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              className={cn(
                // Base prose styling
                "prose prose-lg dark:prose-invert max-w-none",
                // Enhanced typography
                "prose-headings:font-bold prose-headings:tracking-tight prose-headings:scroll-mt-20",
                "prose-h1:text-4xl prose-h1:mb-8 prose-h1:mt-12 prose-h1:leading-tight",
                "prose-h2:text-3xl prose-h2:mb-6 prose-h2:mt-10 prose-h2:leading-tight",
                "prose-h3:text-2xl prose-h3:mb-4 prose-h3:mt-8 prose-h3:leading-tight",
                "prose-h4:text-xl prose-h4:mb-3 prose-h4:mt-6 prose-h4:leading-tight",
                // Gradient headings for better visual hierarchy
                "prose-h1:bg-gradient-to-r prose-h1:from-blue-600 prose-h1:to-cyan-600 prose-h1:bg-clip-text prose-h1:text-transparent",
                "prose-h2:bg-gradient-to-r prose-h2:from-blue-600 prose-h2:to-cyan-600 prose-h2:bg-clip-text prose-h2:text-transparent",
                "prose-h3:text-blue-700 dark:prose-h3:text-blue-400",
                "prose-h4:text-blue-600 dark:prose-h4:text-blue-500",
                // Enhanced paragraph styling
                "prose-p:text-lg prose-p:leading-relaxed prose-p:mb-6 prose-p:text-gray-700 dark:prose-p:text-gray-300",
                "prose-p:first-of-type:text-xl prose-p:first-of-type:font-medium prose-p:first-of-type:text-gray-800 dark:prose-p:first-of-type:text-gray-200",
                // Link styling with blue theme
                "prose-a:text-blue-600 dark:prose-a:text-blue-400 prose-a:no-underline prose-a:font-medium",
                "hover:prose-a:text-blue-700 dark:hover:prose-a:text-blue-300 hover:prose-a:underline",
                "prose-a:transition-colors prose-a:duration-200",
                // Enhanced list styling
                "prose-ul:my-6 prose-ol:my-6",
                "prose-li:text-lg prose-li:leading-relaxed prose-li:mb-2 prose-li:text-gray-700 dark:prose-li:text-gray-300",
                "prose-li:marker:text-blue-600 dark:prose-li:marker:text-blue-400",
                // Enhanced image styling
                "prose-img:rounded-xl prose-img:shadow-2xl prose-img:border prose-img:border-gray-200 dark:prose-img:border-gray-700",
                "prose-img:my-8 prose-img:mx-auto",
                // Enhanced code styling
                "prose-code:text-blue-600 dark:prose-code:text-blue-400 prose-code:bg-blue-50 dark:prose-code:bg-blue-950/30",
                "prose-code:px-2 prose-code:py-1 prose-code:rounded prose-code:font-medium prose-code:text-sm",
                "prose-pre:bg-gradient-to-br prose-pre:from-gray-50 prose-pre:to-gray-100 dark:prose-pre:from-gray-900 dark:prose-pre:to-gray-800",
                "prose-pre:border prose-pre:border-gray-200 dark:prose-pre:border-gray-700 prose-pre:shadow-lg prose-pre:rounded-xl",
                "prose-pre:p-6 prose-pre:my-8",
                // Enhanced blockquote styling
                "prose-blockquote:border-l-4 prose-blockquote:border-blue-500 prose-blockquote:bg-gradient-to-r",
                "prose-blockquote:from-blue-50 prose-blockquote:to-cyan-50 dark:prose-blockquote:from-blue-950/30 dark:prose-blockquote:to-cyan-950/30",
                "prose-blockquote:px-8 prose-blockquote:py-6 prose-blockquote:rounded-r-xl prose-blockquote:shadow-sm",
                "prose-blockquote:my-8 prose-blockquote:italic prose-blockquote:text-gray-700 dark:prose-blockquote:text-gray-300",
                // Table styling
                "prose-table:my-8 prose-table:shadow-lg prose-table:rounded-lg prose-table:overflow-hidden",
                "prose-thead:bg-gradient-to-r prose-thead:from-blue-600 prose-thead:to-cyan-600",
                "prose-th:text-white prose-th:font-semibold prose-th:px-6 prose-th:py-4",
                "prose-td:px-6 prose-td:py-4 prose-td:border-b prose-td:border-gray-200 dark:prose-td:border-gray-700",
                // Strong and emphasis styling
                "prose-strong:text-blue-700 dark:prose-strong:text-blue-400 prose-strong:font-semibold",
                "prose-em:text-gray-600 dark:prose-em:text-gray-400 prose-em:italic"
              )}
            >
              {convertHtmlToMarkdown(blog.content)}
            </ReactMarkdown>
          </div>

          {/* Enhanced Footer */}
          <div className="mt-16 pt-8 border-t border-gradient-to-r from-blue-200 to-cyan-200 dark:from-blue-800 dark:to-cyan-800">
            <div className="flex flex-col sm:flex-row items-center justify-between gap-6">
              <Button
                variant="outline"
                size="lg"
                className="group border-blue-200 hover:border-blue-300 hover:bg-blue-50 dark:border-blue-700 dark:hover:border-blue-600 dark:hover:bg-blue-950/30 transition-all duration-300"
                asChild
              >
                <Link href="/blogs">
                  <ArrowLeft className="w-4 h-4 mr-2 text-blue-600 group-hover:text-blue-700 transition-colors" />
                  <span className="text-blue-600 group-hover:text-blue-700 font-medium">Back to Articles</span>
                </Link>
              </Button>

              {/* Article Stats */}
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Clock className="w-3 h-3" />
                  <span>{readingTime} min read</span>
                </div>
                <div className="flex items-center gap-1">
                  <BookOpen className="w-3 h-3" />
                  <span>{blog.content.split(" ").length} words</span>
                </div>
              </div>
            </div>

            {/* Call to Action */}
            <div className="mt-8 p-6 bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-950/30 dark:to-cyan-950/30 rounded-xl border border-blue-200 dark:border-blue-800">
              <div className="text-center">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                  Enjoyed this article?
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  Explore more insights and stay updated with our latest content.
                </p>
                <div className="flex flex-col sm:flex-row gap-3 justify-center">
                  <Button
                    variant="outline"
                    className="border-blue-300 text-blue-600 hover:bg-blue-600 hover:text-white transition-all duration-300"
                    asChild
                  >
                    <Link href="/blogs">
                      View All Articles
                    </Link>
                  </Button>
                  <Button
                    className="bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white shadow-lg hover:shadow-xl transition-all duration-300"
                    asChild
                  >
                    <Link href="/contact">
                      Get In Touch
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </article>
  );
}
