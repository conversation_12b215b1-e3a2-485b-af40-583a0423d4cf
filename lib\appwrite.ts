import { Client, Databases, ID, Query, Storage } from "appwrite";

// Initialize Appwrite client
const client = new Client()
  .setEndpoint(process.env.NEXT_PUBLIC_APPWRITE_API_ENDPOINT!) // Replace with your Appwrite endpoint
  .setProject(process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID!); // Replace with your project ID

const databases = new Databases(client);
const storage = new Storage(client);

const DATABASE_ID = process.env.NEXT_PUBLIC_DATABASE_ID!; // Replace with your database ID
const COLLECTION_ID = process.env.NEXT_PUBLIC_COLLECTION_ID!; // Replace with your collection ID
const CONTACT_FORM_COLLECTION_ID =
  process.env.NEXT_PUBLIC_APPWRITE_CONTACT_COLLECTION_ID!; // Replace with your contact form collection ID
const BLOGS_COLLECTION_ID =
  process.env.NEXT_PUBLIC_APPWRITE_BLOGS_COLLECTION_ID!; // Replace with your blogs collection ID
const PORTFOLIO_COLLECTION_ID =
  process.env.NEXT_PUBLIC_APPWRITE_PORTFOLIO_COLLECTION_ID!; // Portfolio collection ID
const PORTFOLIO_IMAGES_BUCKET_ID =
  process.env.NEXT_PUBLIC_APPWRITE_PORTFOLIO_IMAGES_BUCKET_ID!; // Portfolio images bucket ID
export interface BookingFormData {
  name: string;
  email: string;
  phone: string;
  date: Date;
  time: string;
  service: string;
  message: string;
}

export interface ContactFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  company: string;
  message: string;
  status: "new" | "read" | "responded";
}

export interface BlogPost {
  title: string;
  content: string;
  excerpt: string;
  slug: string;
  published: boolean;
  featuredImage?: string;
}

export interface PortfolioFeature {
  title: string;
  description: string;
}

export interface PortfolioItem {
  $id: string;
  title: string;
  description: string;
  image: string;
  technologies: string[];
  features: PortfolioFeature[];
  category: string;
  projectUrl?: string;
  slug?: string;
  order?: number;
  published: boolean;
  created_at?: string;
  updated_at?: string;
}

export async function createBooking(data: BookingFormData) {
  try {
    const response = await databases.createDocument(
      DATABASE_ID,
      COLLECTION_ID,
      ID.unique(),
      {
        name: data.name,
        email: data.email,
        phone: data.phone,
        date: data.date.toISOString(),
        time: data.time,
        service: data.service,
        message: data.message,
        status: "pending", // Additional field to track booking status
        createdAt: new Date().toISOString(),
      }
    );

    return response;
  } catch (error) {
    console.error("Error creating booking:", error);
    throw error;
  }
}

export async function submitContactForm(data: ContactFormData) {
  try {
    const response = await databases.createDocument(
      DATABASE_ID,
      CONTACT_FORM_COLLECTION_ID,
      "unique()",
      {
        ...data,
        status: "new",
        createdAt: new Date().toISOString(),
      }
    );
    return response;
  } catch (error) {
    console.error("Error submitting contact form:", error);
    throw error;
  }
}

export const fetchBlogs = async () => {
  try {
    const response = await databases.listDocuments(
      DATABASE_ID,
      BLOGS_COLLECTION_ID,
      [Query.equal("published", true), Query.orderDesc("$createdAt")]
    );
    return response.documents;
  } catch (error) {
    console.error("Error fetching blogs:", error);
  }
};

export async function fetchBlogById(id: string) {
  try {
    const response = await databases.getDocument(
      DATABASE_ID,
      BLOGS_COLLECTION_ID,
      id
    );
    return response;
  } catch (error) {
    console.error("Error fetching blog by ID:", error);
    throw error;
  }
}

export async function fetchBlogBySlug(slug: string) {
  try {
    const response = await databases.listDocuments(
      DATABASE_ID,
      BLOGS_COLLECTION_ID,
      [
        Query.equal("slug", slug),
        Query.equal("published", true),
        Query.limit(1)
      ]
    );

    if (response.documents.length === 0) {
      throw new Error("Blog not found");
    }

    return response.documents[0];
  } catch (error) {
    console.error("Error fetching blog by slug:", error);
    throw error;
  }
}

export async function fetchBlogByIdOrSlug(identifier: string) {
  try {
    // Import the utility function to check if it's a database ID
    const { isValidDatabaseId } = await import('./utils');

    if (isValidDatabaseId(identifier)) {
      // Try to fetch by ID first for backward compatibility
      return await fetchBlogById(identifier);
    } else {
      // Try to fetch by slug
      return await fetchBlogBySlug(identifier);
    }
  } catch (error) {
    console.error("Error fetching blog by ID or slug:", error);
    throw error;
  }
}

export const fetchPortfolioItems = async () => {
  try {
    console.log("Fetching portfolio items with:", {
      DATABASE_ID,
      PORTFOLIO_COLLECTION_ID
    });

    const response = await databases.listDocuments(
      DATABASE_ID,
      PORTFOLIO_COLLECTION_ID,
      [Query.equal("published", true), Query.orderDesc("$createdAt")]
    );

    console.log("Portfolio response:", response);
    console.log("Portfolio documents:", response.documents);

    // Map the response to ensure correct structure
    const items = response.documents.map(doc => {
      console.log("Processing document:", doc);

      // Ensure technologies is an array
      let technologies = doc.technologies || [];
      if (typeof technologies === 'string') {
        try {
          technologies = JSON.parse(technologies);
        } catch {
          technologies = [technologies];
        }
      }

      // Process features
      let features = doc.features || [];
      if (typeof features === 'string') {
        try {
          features = JSON.parse(features);
        } catch {
          features = [];
        }
      }

      // If features is not an array of objects with title and description, set to empty array
      if (!Array.isArray(features) || !features.every(f => typeof f === 'object' && f.title)) {
        features = [];
      }

      return {
        $id: doc.$id,
        title: doc.title || '',
        description: doc.description || '',
        image: doc.image || '',
        technologies: technologies,
        features: features,
        category: doc.category || 'Default',
        projectUrl: doc.projectUrl || '',
        slug: doc.slug || '',
        order: doc.order || 0,
        published: doc.published === false ? false : true,
        created_at: doc.$createdAt,
        updated_at: doc.$updatedAt
      };
    });

    console.log("Processed portfolio items:", items);
    return items;
  } catch (error) {
    console.error("Error fetching portfolio items:", error);
    return [];
  }
};

export async function fetchPortfolioItemById(id: string) {
  try {
    const response = await databases.getDocument(
      DATABASE_ID,
      PORTFOLIO_COLLECTION_ID,
      id
    );
    return response as unknown as PortfolioItem;
  } catch (error) {
    console.error("Error fetching portfolio item by ID:", error);
    throw error;
  }
}

export function getFilePreviewUrl(bucketId: string, fileId: string): string {
  if (!fileId || !bucketId) {
    console.error('Missing bucketId or fileId for getFilePreviewUrl');
    return '';
  }

  try {
    // Use getFileView instead of getFilePreview for better compatibility
    return storage.getFileView(bucketId, fileId).toString();
  } catch (error) {
    console.error('Error getting file view URL:', error);
    // Fallback to manual URL construction
    return `${process.env.NEXT_PUBLIC_APPWRITE_API_ENDPOINT}/storage/buckets/${bucketId}/files/${fileId}/view?project=${process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID}`;
  }
}

// Helper function to test if a file exists and is accessible
export async function testFileAccess(bucketId: string, fileId: string): Promise<boolean> {
  try {
    await storage.getFile(bucketId, fileId);
    return true;
  } catch (error) {
    console.warn(`File ${fileId} is not accessible:`, error);
    return false;
  }
}

export function getPortfolioImageUrl(fileIdOrUrl: string): string {
  if (!fileIdOrUrl) {
    console.warn('No fileId provided to getPortfolioImageUrl, using placeholder');
    return '/placeholder-project.svg';
  }

  // Check if the input is already a full URL
  if (fileIdOrUrl.startsWith('http://') || fileIdOrUrl.startsWith('https://')) {
    console.log('Using provided URL directly:', fileIdOrUrl);
    return fileIdOrUrl;
  }

  // Check if the input contains a URL within it (sometimes happens with copy-paste)
  const urlMatch = fileIdOrUrl.match(/(https?:\/\/[^\s]+)/);
  if (urlMatch) {
    console.log('Extracted URL from string:', urlMatch[0]);
    return urlMatch[0];
  }

  if (!process.env.NEXT_PUBLIC_APPWRITE_API_ENDPOINT) {
    console.warn('NEXT_PUBLIC_APPWRITE_API_ENDPOINT is not defined, using placeholder');
    return '/placeholder-project.svg';
  }

  if (!process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID) {
    console.warn('NEXT_PUBLIC_APPWRITE_PROJECT_ID is not defined, using placeholder');
    return '/placeholder-project.svg';
  }

  if (!PORTFOLIO_IMAGES_BUCKET_ID) {
    console.warn('PORTFOLIO_IMAGES_BUCKET_ID is not defined, using placeholder');
    return '/placeholder-project.svg';
  }

  try {
    // Clean up the fileId in case it has any extra characters
    const cleanFileId = fileIdOrUrl.trim();

    // Validate fileId format (Appwrite file IDs are typically alphanumeric)
    if (!/^[a-zA-Z0-9]+$/.test(cleanFileId)) {
      console.warn(`Invalid fileId format: ${cleanFileId}, using placeholder`);
      return '/placeholder-project.svg';
    }

    // For now, skip the Storage API call to avoid timeouts and use direct URL construction
    // This is more reliable for public buckets
    const url = `${process.env.NEXT_PUBLIC_APPWRITE_API_ENDPOINT}/storage/buckets/${PORTFOLIO_IMAGES_BUCKET_ID}/files/${cleanFileId}/view?project=${process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID}`;
    console.log('Generated direct image URL:', url);
    return url;
  } catch (error) {
    console.warn('Error constructing portfolio image URL, using placeholder:', error);
    return '/placeholder-project.svg';
  }
}
